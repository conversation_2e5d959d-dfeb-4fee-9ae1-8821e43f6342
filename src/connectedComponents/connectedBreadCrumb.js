import { connect } from 'react-redux';
import { pushUrl } from '../actions/navigateActions';
import {
  breadcrumbLevelsMap,
  subPagesOptionsMap,
} from '../constants/breadcrumbConsts';
import BreadCrumb, { buildBreadcrumbOptions } from '../lib/breadCrumb';
import { getCommandBarConfigSelector } from '../selectors/commandBarSelectors';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';

export const mapStateToProps = (state, ownProps) => {
  const { pageAlias, masterPageOptions, getPageState } = ownProps;
  const { masterPageAlias, masterPageDisplayName } = masterPageOptions;
  const { params } = state[pageAlias].pageState;
  const masterPageParams = (getPageState(state, masterPageAlias) || {}).params;
  const config = getCommandBarConfigSelector(state, JOBS_PAGE_ALIAS);
  const aliasPageDisplayName = config.pageTitleSection.pageTitle;

  // Check if listPageAndBulkUpdate feature flag is enabled
  const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(
    FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE
  )(state);

  // Get the appropriate display name for breadcrumb
  let effectiveDisplayName = aliasPageDisplayName;
  if (listPageAndBulkUpdateFeatureFlag) {
    // When feature flag is enabled, use 'Jobs' translation instead of 'Lists'
    const { jobsPage } = getTranslationsSelector(state, {
      sectionName: 'pages',
    });
    effectiveDisplayName = jobsPage || aliasPageDisplayName;
  }

  const breadcrumbOptions = params
    ? buildBreadcrumbOptions(
        breadcrumbLevelsMap[pageAlias],
        params,
        subPagesOptionsMap[masterPageDisplayName],
        masterPageParams,
        masterPageAlias,
        effectiveDisplayName
      )
    : [];
  const sepratatorIconType = 'slash';

  return {
    breadcrumbOptions,
    sepratatorIconType,
  };
};

export const mapDispatchToProps = (dispatch) => {
  return {
    onClick: (newParams, page) => {
      dispatch(pushUrl(newParams, page));
    },
  };
};

const ConnectedBreadcrumb = connect(
  mapStateToProps,
  mapDispatchToProps
)(BreadCrumb);

export default ConnectedBreadcrumb;
