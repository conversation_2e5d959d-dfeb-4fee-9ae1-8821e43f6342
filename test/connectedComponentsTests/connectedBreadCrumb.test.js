import { mapStateToProps } from '../../src/connectedComponents/connectedBreadCrumb';
import { FEATURE_FLAGS } from '../../src/constants/globalConsts';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE } from '../../src/constants/jobsPageConsts';
import { HOT_KEYS_HELP_WINDOW_SECTIONS } from '../../src/constants/hotKeysConsts';

const mockState = {
  rolegroupListPage: {
    pageState: {
      params: {
        jobName: 'Test Job',
        jobSurrogateId: 123
      }
    }
  },
  featureManagement: {
    features: []
  },
  internationalization: {
    translation: {
      pages: {
        jobsPage: 'Jobs'
      }
    }
  },
  commandBar: {
    [JOBS_PAGE_ALIAS]: {
      pageTitleSection: {
        pageTitle: 'Lists'
      }
    }
  }
};

const mockOwnProps = {
  pageAlias: ROLE_GROUP_LIST_PAGE,
  masterPageOptions: {
    masterPageAlias: JOBS_PAGE_ALIAS,
    masterPageDisplayName: HOT_KEYS_HELP_WINDOW_SECTIONS.JOBS
  },
  getPageState: jest.fn(() => ({ params: { page: 1, pagesize: 20 } }))
};

describe('connectedBreadCrumb mapStateToProps', () => {
  it('should use Lists when feature flag is disabled', () => {
    const result = mapStateToProps(mockState, mockOwnProps);
    
    expect(result.breadcrumbOptions).toHaveLength(2);
    expect(result.breadcrumbOptions[0].description).toBe('Lists');
    expect(result.breadcrumbOptions[1].description).toBe('Test Job');
  });

  it('should use Jobs translation when feature flag is enabled', () => {
    const stateWithFeatureFlag = {
      ...mockState,
      featureManagement: {
        features: [
          {
            name: FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE,
            enabled: true
          }
        ]
      }
    };

    const result = mapStateToProps(stateWithFeatureFlag, mockOwnProps);
    
    expect(result.breadcrumbOptions).toHaveLength(2);
    expect(result.breadcrumbOptions[0].description).toBe('Jobs');
    expect(result.breadcrumbOptions[1].description).toBe('Test Job');
  });

  it('should fallback to original display name if translation is not available', () => {
    const stateWithFeatureFlagNoTranslation = {
      ...mockState,
      featureManagement: {
        features: [
          {
            name: FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE,
            enabled: true
          }
        ]
      },
      internationalization: {
        translation: {
          pages: {}
        }
      }
    };

    const result = mapStateToProps(stateWithFeatureFlagNoTranslation, mockOwnProps);
    
    expect(result.breadcrumbOptions).toHaveLength(2);
    expect(result.breadcrumbOptions[0].description).toBe('Lists');
    expect(result.breadcrumbOptions[1].description).toBe('Test Job');
  });

  it('should return empty breadcrumb options when params are not available', () => {
    const stateWithoutParams = {
      ...mockState,
      rolegroupListPage: {
        pageState: {}
      }
    };

    const result = mapStateToProps(stateWithoutParams, mockOwnProps);
    
    expect(result.breadcrumbOptions).toHaveLength(0);
  });
});
